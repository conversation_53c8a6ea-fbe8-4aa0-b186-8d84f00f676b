import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DynamicForm from './DynamicForm.tsx';
import {describe, expect, it} from 'vitest';
import type {Schema} from "../types/Schema.ts";
import userEvent from '@testing-library/user-event';

const testSchema: Schema = {
    title: "User Registration",
    fields: [
        {label: "Name", type: "text", name: "name"},
        {label: "Dob", type: "date", name: "dob", required: true},
        {label: "Subscribe", type: "checkbox", name: "subscribe"},
        {
            label: "Gender",
            type: "select",
            name: "gender",
            options: ["Male", "Female", "Other"]
        }
    ]
};

describe('DynamicForm', () => {
    it('renders the form correctly', () => {
        render(<DynamicForm schemaText={JSON.stringify(testSchema)}/>);
        expect(screen.getByText('User Registration')).toBeInTheDocument();
        expect(screen.getByLabelText('Name')).toBeInTheDocument();
        expect(screen.getByLabelText('Dob')).toBeInTheDocument();
        expect(screen.getByLabelText('Subscribe')).toBeInTheDocument();
        expect(screen.getByLabelText('Gender')).toBeInTheDocument();
    });

    it('validates required fields on submit', async () => {
        render(<DynamicForm schemaText={JSON.stringify(testSchema)}/>);
        await userEvent.click(screen.getByRole('button', {name: /submit/i}));
        expect(await screen.findByText('Dob is required')).toBeInTheDocument();

        const submittedDataDiv = screen.queryByTestId('submitted-data');
        expect(submittedDataDiv).not.toBeInTheDocument();
    });

    it('submits the form correctly', async () => {
        render(<DynamicForm schemaText={JSON.stringify(testSchema)}/>);
        await userEvent.type(screen.getByLabelText('Name'), 'John Doe');
        await userEvent.type(screen.getByLabelText('Dob'), '2000-01-01');
        await userEvent.click(screen.getByLabelText('Subscribe'));
        await userEvent.selectOptions(screen.getByLabelText('Gender'), 'Male');
        await userEvent.click(screen.getByRole('button', {name: /submit/i}));
        expect(screen.getByTestId('submitted-data')).toBeInTheDocument();
        expect(screen.getByTestId('submitted-data')).toHaveTextContent('"name": "John Doe"');
        expect(screen.getByTestId('submitted-data')).toHaveTextContent('"dob": "2000-01-01"');
        expect(screen.getByTestId('submitted-data')).toHaveTextContent('"subscribe": true');
        expect(screen.getByTestId('submitted-data')).toHaveTextContent('"gender": "Male"');
    });
});